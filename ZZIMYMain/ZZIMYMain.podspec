# MARK: converted automatically by spec.py. @hgy

Pod::Spec.new do |s|
    s.name = 'ZZIMYMain'
    s.version = '8.96.003'
    s.description = 'Demo Description'
    s.license = 'MIT'
    s.summary = 'Seeyou'
    s.homepage = 'https://github.com/meiyoudev/IMYPublic'
    s.authors = { 'xyz' => '<EMAIL>' }
    s.requires_arc = true
    s.ios.deployment_target = '12.0'
    
    s.source = { :git => '*********************:iOS/ZZIMYMain.git', :branch => 'release-8.96.0' }

    s.default_subspec  = ['Seeyou','Rights','Widget','Watch']

    s.subspec "Seeyou" do |ss|
      ss.source_files = 'Seeyou/Classes/**/*.{h,m,c}'
      ss.resources = 'Seeyou/Resource/**/*.{json,png,jpg,gif,xib,db,plist,js,html,strings,storyboard,m4a,caf,css,txt,order,eot,svg,ttf,woff,sqlite,mp3,pag,apng}',
                     'Seeyou/Classes/**/*.{json,png,jpg,gif,xib,db,plist,js,html,strings,storyboard,m4a,caf,css,txt,order,eot,svg,ttf,woff,sqlite,mp3}',
                     'Seeyou/Bundles/*.{bundle,xcassets}'
    end

    s.subspec "Rights" do |ss|
      ss.source_files = 'Rights/Classes/**/*.{h,m}'
      ss.resources = 'Rights/Resource/**/*.{json,png,jpg,gif,xib,db,plist,js,html,strings,storyboard,m4a,caf,css,txt,order,eot,svg,ttf,woff,sqlite,mp3,pag,apng}',
                     'Rights/Bundles/*.{bundle,xcassets}'
    end
    
    s.subspec "Widget" do |ss|
      ss.source_files = 'Widget/Classes/**/*.{h,m}'
      ss.resources = ''
    end
    s.subspec "Watch" do |ss|
      ss.source_files = 'Watch/Classes/**/*.{h,m}'
      ss.resources = ''
    end
    # 二进制库 只认根阶段的资源配置，这边把 subspec 的配置拷贝一份
    s.resources = 'Seeyou/Resource/**/*.{json,png,jpg,gif,xib,db,plist,js,html,strings,storyboard,m4a,caf,css,txt,order,eot,svg,ttf,woff,sqlite,mp3,pag,apng}',
                  'Seeyou/Classes/**/*.{json,png,jpg,gif,xib,db,plist,js,html,strings,storyboard,m4a,caf,css,txt,order,eot,svg,ttf,woff,sqlite,mp3}',
                  'Seeyou/Bundles/*.{bundle,xcassets}',
                  'Rights/Resource/**/*.{json,png,jpg,gif,xib,db,plist,js,html,strings,storyboard,m4a,caf,css,txt,order,eot,svg,ttf,woff,sqlite,mp3,pag,apng}',
                  'Rights/Bundles/*.{bundle,xcassets}'
    
    s.dependency 'React'
    s.dependency 'DoubleConversion'
    s.dependency 'IMYBaseKit'
    
    s.dependency 'IMYLamaHome'
    s.dependency 'IMYMiniProgram'
    s.dependency 'IMYMSGJump'
    s.dependency 'BBJBabyHome'
    s.dependency 'BBJViewKit'
    s.dependency 'IMY_EBusiness'
    s.dependency 'Poseidon'
    
    s.dependency 'IMYMPN'
    s.dependency 'IMYMSG'
    s.dependency 'IMYMWPhotoBrowser'
    s.dependency 'IMYAccount'
    s.dependency 'IMYAnswer'
    s.dependency 'IMYTools'
    s.dependency 'IMYTTQ'
    s.dependency 'IMYCCAPI'
    s.dependency 'IMYNews'
    s.dependency 'IMYRecord'
    s.dependency 'IMYSVR'
    s.dependency 'IMYYQHome'
    s.dependency 'IMYKnowledge'
    s.dependency 'ChatAI'

end
