#!/bin/bash

# MeetYouApp本地Pod库分支同步脚本
# 功能：根据主项目分支，自动切换本地库到相同分支并拉取最新代码

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v git &> /dev/null; then
        log_error "Git未安装，请先安装Git"
        exit 1
    fi
    
    if ! command -v pod &> /dev/null; then
        log_error "CocoaPods未安装，请先安装CocoaPods"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 获取当前分支
get_current_branch() {
    local branch
    branch=$(git branch --show-current 2>/dev/null)
    if [ -z "$branch" ]; then
        log_error "无法获取当前分支，请确保在Git仓库中"
        exit 1
    fi
    echo "$branch"
}

# 检查分支是否存在
check_branch_exists() {
    local repo_path="$1"
    local branch="$2"
    
    cd "$repo_path"
    if git show-ref --verify --quiet "refs/heads/$branch"; then
        return 0
    elif git show-ref --verify --quiet "refs/remotes/origin/$branch"; then
        return 0
    else
        return 1
    fi
}

# 切换分支并拉取代码
switch_and_pull() {
    local repo_path="$1"
    local branch="$2"
    local lib_name="$3"

    log_info "处理库: $lib_name"

    if [ ! -d "$repo_path" ]; then
        log_warning "路径不存在: $repo_path，跳过"
        return 1
    fi

    cd "$repo_path"

    # 检查是否是Git仓库
    if [ ! -d ".git" ]; then
        log_warning "$lib_name 不是Git仓库，跳过"
        return 1
    fi

    # 保存当前状态
    local current_branch
    current_branch=$(git branch --show-current 2>/dev/null || echo "")

    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD -- 2>/dev/null; then
        log_warning "$lib_name 有未提交的更改，跳过自动切换"
        return 1
    fi

    # 检查目标分支是否存在
    if check_branch_exists "$repo_path" "$branch"; then
        log_info "切换到分支: $branch"

        # 尝试切换分支
        if git checkout "$branch" 2>/dev/null; then
            log_success "成功切换到分支: $branch"

            # 拉取最新代码
            if git pull origin "$branch" 2>/dev/null; then
                log_success "$lib_name 代码更新完成"
            else
                log_warning "$lib_name 代码拉取失败，但分支切换成功"
            fi
        else
            log_error "$lib_name 分支切换失败"
            return 1
        fi
    else
        log_warning "$lib_name 中不存在分支 '$branch'，保持当前分支"
        return 1
    fi

    return 0
}

# 解析Podfile.lib文件
parse_podfile_lib() {
    local podfile_path="$1"
    local enabled_libs=()
    
    if [ ! -f "$podfile_path" ]; then
        log_error "Podfile.lib文件不存在: $podfile_path"
        exit 1
    fi
    
    log_info "解析Podfile.lib文件..."
    
    # 读取文件并解析启用的库
    while IFS= read -r line; do
        # 跳过注释和空行
        if [[ "$line" =~ ^[[:space:]]*# ]] || [[ -z "${line// }" ]]; then
            continue
        fi
        
        # 查找变量设置为true的行
        if [[ "$line" =~ ^\$([a-zA-Z_][a-zA-Z0-9_]*)[[:space:]]*=[[:space:]]*true ]]; then
            local var_name="${BASH_REMATCH[1]}"
            enabled_libs+=("$var_name")
        fi
    done < "$podfile_path"
    
    echo "${enabled_libs[@]}"
}

# 获取库的路径
get_lib_path() {
    local lib_name="$1"
    local podfile_path="$2"

    # 在Podfile.lib中查找对应的路径
    while IFS= read -r line; do
        if [[ "$line" =~ pod[[:space:]]+\'([^\']*)\'][[:space:]]*,[[:space:]]*:path[[:space:]]*=\>[[:space:]]*\'([^\']*)\' ]]; then
            local pod_name="${BASH_REMATCH[1]}"
            local pod_path="${BASH_REMATCH[2]}"

            # 简单匹配库名（可能需要更精确的匹配逻辑）
            if [[ "$pod_name" == *"$lib_name"* ]] || [[ "$lib_name" == *"$pod_name"* ]]; then
                echo "$pod_path"
                return 0
            fi
        fi
    done < "$podfile_path"

    # 如果没找到，尝试常见的路径模式
    echo "../$lib_name"
}

# 主函数
main() {
    log_info "开始MeetYouApp本地Pod库分支同步"
    
    # 检查依赖
    check_dependencies
    
    # 获取当前分支
    local current_branch
    current_branch=$(get_current_branch)
    log_info "当前分支: $current_branch"
    
    # 解析Podfile.lib
    local podfile_lib_path="MeetYouApp/Podfile.lib"
    if [ ! -f "$podfile_lib_path" ]; then
        podfile_lib_path="Podfile.lib"
    fi
    
    local enabled_libs
    enabled_libs=($(parse_podfile_lib "$podfile_lib_path"))
    
    if [ ${#enabled_libs[@]} -eq 0 ]; then
        log_warning "没有找到启用的本地库"
        exit 0
    fi
    
    log_info "找到 ${#enabled_libs[@]} 个启用的本地库: ${enabled_libs[*]}"
    
    # 记录处理结果
    local success_count=0
    local skip_count=0
    local error_count=0
    
    # 保存当前目录
    local original_dir="$PWD"
    
    # 处理每个启用的库
    for lib_name in "${enabled_libs[@]}"; do
        local lib_path
        lib_path=$(get_lib_path "$lib_name" "$podfile_lib_path")

        if switch_and_pull "$lib_path" "$current_branch" "$lib_name"; then
            ((success_count++))
        else
            ((skip_count++))
        fi

        # 回到原始目录
        cd "$original_dir"
    done
    
    # 输出处理结果
    echo ""
    log_info "处理结果统计:"
    log_success "成功: $success_count"
    log_warning "跳过: $skip_count"
    log_error "错误: $error_count"
    
    # 执行pod update
    echo ""
    log_info "开始执行pod update..."
    
    # 检查是否在MeetYouApp目录中
    if [ -f "MeetYouApp/Podfile" ]; then
        cd MeetYouApp
    elif [ ! -f "Podfile" ]; then
        log_error "找不到Podfile，请确保在正确的目录中运行脚本"
        exit 1
    fi
    
    if pod update --no-repo-update; then
        log_success "Pod update完成"
    else
        log_error "Pod update失败"
        exit 1
    fi
    
    echo ""
    log_success "所有操作完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 